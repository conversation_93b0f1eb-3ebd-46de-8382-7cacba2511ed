using Microsoft.Extensions.Options;
using SilvrBear_Amazon_Automation.Models;
using System.Text.Json;

namespace SilvrBear_Amazon_Automation.Services;

/// <summary>
/// Implementation of Amazon Seller API service using Fulfillment Inbound API v2024-03-20
/// </summary>
public class AmazonSellerApiService : IAmazonSellerApiService
{
    private readonly AmazonApiClient _apiClient;
    private readonly AmazonCredentials _credentials;
    private readonly ILogger<AmazonSellerApiService> _logger;

    public AmazonSellerApiService(
        AmazonApiClient apiClient,
        IOptions<AmazonCredentials> credentials,
        ILogger<AmazonSellerApiService> logger)
    {
        _apiClient = apiClient;
        _credentials = credentials.Value;
        _logger = logger;
    }

    /// <summary>
    /// Creates an inbound plan using the new v2024-03-20 API
    /// This replaces the old createInboundShipmentPlan operation
    /// </summary>
    public async Task<AmazonApiResponse<InboundShipmentPlanResponse>> CreateInboundShipmentPlanAsync(
        List<ShipmentItem> items,
        Address shipFromAddress,
        string labelPrepPreference = "SELLER_LABEL")
    {
        try
        {
            // Debug logging for method entry and parameters
            _logger.LogInformation("=== CreateInboundShipmentPlanAsync DEBUG ===");
            _logger.LogInformation("Method Entry - Items Count: {ItemCount}", items.Count);
            _logger.LogInformation("Items Details: {Items}", System.Text.Json.JsonSerializer.Serialize(items));
            _logger.LogInformation("Ship From Address: {Address}", System.Text.Json.JsonSerializer.Serialize(shipFromAddress));
            _logger.LogInformation("Label Prep Preference: {LabelPref}", labelPrepPreference);
            _logger.LogInformation("Marketplace ID: {MarketplaceId}", _credentials.MarketplaceId);

            // Step 1: Create inbound plan using new v2024-03-20 API
            var endpoint = "/inbound/fba/2024-03-20/inboundPlans";

            var requestPayload = new
            {
                name = $"Inbound Plan {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}",
                sourceAddress = new
                {
                    name = shipFromAddress.Name,
                    addressLine1 = shipFromAddress.AddressLine1,
                    addressLine2 = shipFromAddress.AddressLine2,
                    city = shipFromAddress.City,
                    stateOrProvinceCode = shipFromAddress.StateOrProvinceCode,
                    countryCode = shipFromAddress.CountryCode,
                    postalCode = shipFromAddress.PostalCode,
                    phoneNumber = shipFromAddress.PhoneNumber // Required for v2024-03-20 API
                },
                destinationMarketplaces = new[] { _credentials.MarketplaceId },
                items = items.Select(item => new
                {
                    msku = item.Sku,
                    quantity = item.Quantity,
                    labelOwner = labelPrepPreference == "SELLER_LABEL" ? "SELLER" : "AMAZON",
                    prepOwner = "NONE"
                }).ToList()
            };

            _logger.LogInformation("Request Payload Prepared: {Payload}", System.Text.Json.JsonSerializer.Serialize(requestPayload));

            var result = await _apiClient.PostDirectAsync<InboundShipmentPlanResponse>(endpoint, requestPayload);
            
            _logger.LogInformation("=== CreateInboundShipmentPlanAsync RESULT ===");
            _logger.LogInformation("Success: {Success}", result.IsSuccess);
            _logger.LogInformation("Errors Count: {ErrorCount}", result.Errors?.Count ?? 0);
            if (result.Errors?.Any() == true)
            {
                _logger.LogInformation("Errors: {Errors}", System.Text.Json.JsonSerializer.Serialize(result.Errors));
            }
            if (result.Payload != null)
            {
                _logger.LogInformation("Response Payload: {Payload}", System.Text.Json.JsonSerializer.Serialize(result.Payload));
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating inbound plan");
            throw;
        }
    }

    /// <summary>
    /// Creates an inbound plan with specific destination for India marketplace using the new v2024-03-20 API
    /// This includes CustomPlacement details required for IN marketplace
    /// </summary>
    public async Task<AmazonApiResponse<InboundShipmentPlanResponse>> CreateInboundShipmentPlanAsync(
        List<ShipmentItem> items,
        Address shipFromAddress,
        string destinationFulfillmentCenterId,
        string labelPrepPreference = "SELLER_LABEL")
    {
        try
        {
            // Step 1: Create inbound plan using new v2024-03-20 API with CustomPlacement for India
            var endpoint = "/inbound/fba/2024-03-20/inboundPlans";

            var requestPayload = new
            {
                name = $"Inbound Plan {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}",
                sourceAddress = new
                {
                    name = shipFromAddress.Name,
                    addressLine1 = shipFromAddress.AddressLine1,
                    addressLine2 = shipFromAddress.AddressLine2,
                    city = shipFromAddress.City,
                    stateOrProvinceCode = shipFromAddress.StateOrProvinceCode,
                    countryCode = shipFromAddress.CountryCode,
                    postalCode = shipFromAddress.PostalCode,
                    phoneNumber = shipFromAddress.PhoneNumber // Required for v2024-03-20 API
                },
                destinationMarketplaces = new[] { _credentials.MarketplaceId },
                items = items.Select(item => new
                {
                    msku = item.Sku,
                    quantity = item.Quantity,
                    labelOwner = labelPrepPreference == "SELLER_LABEL" ? "SELLER" : "AMAZON",
                    prepOwner = "NONE"
                }).ToList()
                // NOTE: For India marketplace, destination fulfillment center is specified in Step 2 (generatePlacementOptions)
            };

            _logger.LogInformation("Creating inbound plan with {ItemCount} items for India marketplace (FC: {FulfillmentCenter}) using v2024-03-20 API", 
                items.Count, destinationFulfillmentCenterId);

            return await _apiClient.PostDirectAsync<InboundShipmentPlanResponse>(endpoint, requestPayload);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating inbound plan for India marketplace");
            throw;
        }
    }

    /// <summary>
    /// Creates an inbound shipment using the new v2024-03-20 API workflow
    /// This involves multiple steps: generate packing options, confirm packing, confirm placement
    /// </summary>
    public async Task<AmazonApiResponse<CreateInboundShipmentResponse>> CreateInboundShipmentAsync(
        string inboundPlanId,
        CreateInboundShipmentRequest request)
    {
        try
        {
            _logger.LogInformation("=== CreateInboundShipmentAsync WORKFLOW DEBUG ===");
            _logger.LogInformation("Starting inbound shipment creation for plan {InboundPlanId} with name {ShipmentName}",
                inboundPlanId, request.ShipmentName);
            _logger.LogInformation("Request Details: {Request}", System.Text.Json.JsonSerializer.Serialize(request));

            // Step 1: Generate packing options
            _logger.LogInformation("STEP 1: Generating packing options...");
            var packingOptionsResponse = await GeneratePackingOptionsAsync(inboundPlanId);
            if (!packingOptionsResponse.IsSuccess)
            {
                _logger.LogWarning("STEP 1 FAILED: Failed to generate packing options for plan {InboundPlanId}: {Errors}",
                    inboundPlanId, string.Join(", ", packingOptionsResponse.Errors.Select(e => e.Message)));
                return new AmazonApiResponse<CreateInboundShipmentResponse>
                {
                    Errors = EnhanceWorkflowErrors(packingOptionsResponse.Errors, "packing options generation")
                };
            }
            _logger.LogInformation("STEP 1 SUCCESS: Packing options generated successfully");

            // Step 2: List and select packing options
            _logger.LogInformation("STEP 2: Listing packing options...");
            var packingOptions = await ListPackingOptionsAsync(inboundPlanId);
            if (!packingOptions.IsSuccess || packingOptions.Payload?.PackingOptions?.Any() != true)
            {
                _logger.LogWarning("STEP 2 FAILED: No packing options available for plan {InboundPlanId}", inboundPlanId);
                return new AmazonApiResponse<CreateInboundShipmentResponse>
                {
                    Errors = new List<ApiError> { new() { Code = "NoPackingOptions", Message = "No packing options available" } }
                };
            }

            // Step 3: Confirm packing option (select the first available option)
            var selectedPackingOption = packingOptions.Payload!.PackingOptions.First();
            _logger.LogInformation("STEP 3: Confirming packing option {PackingOptionId}...", selectedPackingOption.PackingOptionId);
            var confirmPackingResponse = await ConfirmPackingOptionAsync(inboundPlanId, selectedPackingOption.PackingOptionId);
            if (!confirmPackingResponse.IsSuccess)
            {
                _logger.LogWarning("STEP 3 FAILED: Failed to confirm packing option {PackingOptionId}", selectedPackingOption.PackingOptionId);
                return new AmazonApiResponse<CreateInboundShipmentResponse>
                {
                    Errors = confirmPackingResponse.Errors
                };
            }
            _logger.LogInformation("STEP 3 SUCCESS: Packing option confirmed successfully");

            // Step 4: Confirm placement option
            _logger.LogInformation("STEP 4: Confirming placement option...");
            var confirmPlacementResponse = await ConfirmPlacementOptionAsync(inboundPlanId, selectedPackingOption.PackingOptionId);

            _logger.LogInformation("=== CreateInboundShipmentAsync WORKFLOW RESULT ===");
            _logger.LogInformation("Final Result - Success: {Success}", confirmPlacementResponse.IsSuccess);
            _logger.LogInformation("Final Result - Errors Count: {ErrorCount}", confirmPlacementResponse.Errors?.Count ?? 0);
            if (confirmPlacementResponse.Errors?.Any() == true)
            {
                _logger.LogInformation("Final Result - Errors: {Errors}", System.Text.Json.JsonSerializer.Serialize(confirmPlacementResponse.Errors));
            }
            if (confirmPlacementResponse.Payload != null)
            {
                _logger.LogInformation("Final Result - Response Payload: {Payload}", System.Text.Json.JsonSerializer.Serialize(confirmPlacementResponse.Payload));
            }

            _logger.LogInformation("Completed inbound shipment creation for plan {InboundPlanId}", inboundPlanId);

            return confirmPlacementResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating inbound shipment for plan {InboundPlanId}", inboundPlanId);
            throw;
        }
    }

    /// <summary>
    /// Updates inbound shipment packing information using v2024-03-20 API
    /// This replaces the old updateInboundShipment operation
    /// </summary>
    public async Task<AmazonApiResponse<object>> UpdateInboundShipmentAsync(
        string inboundPlanId,
        CreateInboundShipmentRequest request)
    {
        try
        {
            // Debug logging for method entry and parameters
            _logger.LogInformation("=== UpdateInboundShipmentAsync DEBUG ===");
            _logger.LogInformation("Method Entry - Inbound Plan ID: {InboundPlanId}", inboundPlanId);
            _logger.LogInformation("Request Details: {Request}", System.Text.Json.JsonSerializer.Serialize(request));
            _logger.LogInformation("Boxes Count: {BoxCount}", request.Boxes?.Count ?? 0);

            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingInformation";

            var requestPayload = new
            {
                packageGroupings = (request.Boxes ?? new List<ShipmentBox>()).Select(box => new
                {
                    packingGroupId = box.BoxId,
                    boxes = new[]
                    {
                        new
                        {
                            contentInformationSource = "BOX_CONTENT_PROVIDED",
                            boxId = box.BoxId,
                            weight = new
                            {
                                value = box.Weight.Value,
                                unit = box.Weight.Unit.ToUpper()
                            },
                            dimensions = new
                            {
                                length = box.Dimensions.Length,
                                width = box.Dimensions.Width,
                                height = box.Dimensions.Height,
                                unitOfMeasurement = box.Dimensions.Unit.ToUpper()
                            },
                            items = (box.Contents ?? new List<BoxContent>()).Select(item => new
                            {
                                msku = item.Sku,
                                quantity = item.Quantity
                            }).ToList()
                        }
                    }
                }).ToList()
            };

            _logger.LogInformation("Packing Information Payload: {Payload}", System.Text.Json.JsonSerializer.Serialize(requestPayload));

            var result = await _apiClient.PostAsync<object>(endpoint, requestPayload);

            _logger.LogInformation("=== UpdateInboundShipmentAsync RESULT ===");
            _logger.LogInformation("Success: {Success}", result.IsSuccess);
            _logger.LogInformation("Errors Count: {ErrorCount}", result.Errors?.Count ?? 0);
            if (result.Errors?.Any() == true)
            {
                _logger.LogInformation("Errors: {Errors}", System.Text.Json.JsonSerializer.Serialize(result.Errors));
            }
            if (result.Payload != null)
            {
                _logger.LogInformation("Response Payload: {Payload}", System.Text.Json.JsonSerializer.Serialize(result.Payload));
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting packing information for plan {InboundPlanId}", inboundPlanId);
            throw;
        }
    }

    public async Task<AmazonApiResponse<GetShipmentResponse>> GetInboundShipmentAsync(string shipmentId)
    {
        try
        {
            var endpoint = $"/fba/inbound/v0/shipments/{shipmentId}";
            
            _logger.LogInformation("Getting inbound shipment details for {ShipmentId}", shipmentId);

            return await _apiClient.GetAsync<GetShipmentResponse>(endpoint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting inbound shipment {ShipmentId}", shipmentId);
            throw;
        }
    }

    public async Task<AmazonApiResponse<List<GetShipmentResponse>>> GetInboundShipmentsAsync(
        List<string>? shipmentStatusList = null,
        List<string>? shipmentIdList = null,
        DateTime? lastUpdatedAfter = null,
        DateTime? lastUpdatedBefore = null)
    {
        try
        {
            var endpoint = "/fba/inbound/v0/shipments";
            var queryParams = new Dictionary<string, string>
            {
                ["MarketplaceId"] = _credentials.MarketplaceId
            };

            if (shipmentStatusList?.Any() == true)
                queryParams["ShipmentStatusList"] = string.Join(",", shipmentStatusList);

            if (shipmentIdList?.Any() == true)
                queryParams["ShipmentIdList"] = string.Join(",", shipmentIdList);

            if (lastUpdatedAfter.HasValue)
                queryParams["LastUpdatedAfter"] = lastUpdatedAfter.Value.ToString("yyyy-MM-ddTHH:mm:ssZ");

            if (lastUpdatedBefore.HasValue)
                queryParams["LastUpdatedBefore"] = lastUpdatedBefore.Value.ToString("yyyy-MM-ddTHH:mm:ssZ");

            _logger.LogInformation("Getting inbound shipments list");

            // First try with the wrapped response format
            var wrappedResponse = await _apiClient.GetAsync<ShipmentsListResponse>(endpoint, queryParams);
            
            if (wrappedResponse.IsSuccess && wrappedResponse.Payload != null)
            {
                return new AmazonApiResponse<List<GetShipmentResponse>>
                {
                    Payload = wrappedResponse.Payload.ShipmentData,
                    Errors = wrappedResponse.Errors
                };
            }
            
            // If that fails, try with direct array response
            var directResponse = await _apiClient.GetAsync<List<GetShipmentResponse>>(endpoint, queryParams);
            
            if (directResponse.IsSuccess && directResponse.Payload != null)
            {
                return new AmazonApiResponse<List<GetShipmentResponse>>
                {
                    Payload = directResponse.Payload,
                    Errors = directResponse.Errors
                };
            }
            
            // Return the error from the wrapped response attempt
            return new AmazonApiResponse<List<GetShipmentResponse>>
            {
                Payload = new List<GetShipmentResponse>(),
                Errors = wrappedResponse.Errors.Count != 0 ? wrappedResponse.Errors : directResponse.Errors
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting inbound shipments list");
            throw;
        }
    }

    /// <summary>
    /// Generate transportation options using v2024-03-20 API
    /// This replaces the old putTransportDetails + estimateTransport operations
    /// </summary>
    public async Task<AmazonApiResponse<object>> GenerateTransportationOptionsAsync(string inboundPlanId)
    {
        try
        {
            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/transportationOptions";

            _logger.LogInformation("Generating transportation options for inbound plan {InboundPlanId}", inboundPlanId);

            return await _apiClient.PostAsync<object>(endpoint, new { });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating transportation options for plan {InboundPlanId}", inboundPlanId);
            throw;
        }
    }

    /// <summary>
    /// List available transportation options using v2024-03-20 API
    /// </summary>
    public async Task<AmazonApiResponse<object>> ListTransportationOptionsAsync(string inboundPlanId)
    {
        try
        {
            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/transportationOptions";

            _logger.LogInformation("Listing transportation options for inbound plan {InboundPlanId}", inboundPlanId);

            return await _apiClient.GetAsync<object>(endpoint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error listing transportation options for plan {InboundPlanId}", inboundPlanId);
            throw;
        }
    }

    /// <summary>
    /// Confirm transportation options using v2024-03-20 API
    /// This replaces the old confirmTransport operation
    /// </summary>
    public async Task<AmazonApiResponse<object>> ConfirmTransportationOptionsAsync(string inboundPlanId, string transportationOptionId)
    {
        try
        {
            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/transportationOptions/{transportationOptionId}/confirmation";

            _logger.LogInformation("Confirming transportation option {TransportationOptionId} for plan {InboundPlanId}",
                transportationOptionId, inboundPlanId);

            return await _apiClient.PostAsync<object>(endpoint, new { });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error confirming transportation option {TransportationOptionId} for plan {InboundPlanId}",
                transportationOptionId, inboundPlanId);
            throw;
        }
    }

    public async Task<AmazonApiResponse<object>> GetLabelsAsync(
        string shipmentId,
        string pageType = "PackageLabel_Letter_6",
        string labelType = "UNIQUE",
        int? numberOfPackages = null)
    {
        try
        {
            var endpoint = $"/fba/inbound/v0/shipments/{shipmentId}/labels";
            var queryParams = new Dictionary<string, string>
            {
                ["PageType"] = pageType,
                ["LabelType"] = labelType
            };

            if (numberOfPackages.HasValue)
                queryParams["NumberOfPackages"] = numberOfPackages.Value.ToString();

            _logger.LogInformation("Getting labels for shipment {ShipmentId}", shipmentId);

            return await _apiClient.GetAsync<object>(endpoint, queryParams);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting labels for shipment {ShipmentId}", shipmentId);
            throw;
        }
    }

    public async Task<AmazonApiResponse<object>> GetBillOfLadingAsync(string shipmentId)
    {
        try
        {
            var endpoint = $"/fba/inbound/v0/shipments/{shipmentId}/billOfLading";
            
            _logger.LogInformation("Getting bill of lading for shipment {ShipmentId}", shipmentId);

            return await _apiClient.GetAsync<object>(endpoint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting bill of lading for shipment {ShipmentId}", shipmentId);
            throw;
        }
    }

    public async Task<bool> ValidateCredentialsAsync()
    {
        try
        {
            _logger.LogInformation("=== ValidateCredentialsAsync DEBUG ===");
            _logger.LogInformation("Starting credential validation using preserved v0 endpoint");
            _logger.LogInformation("Marketplace ID: {MarketplaceId}", _credentials.MarketplaceId);
            _logger.LogInformation("Base URL: {BaseUrl}", _credentials.BaseUrl);

            // Use the preserved v0 endpoint for credential validation
            // This endpoint is still available and works for authentication testing
            var endpoint = "/fba/inbound/v0/shipments";
            var queryParams = new Dictionary<string, string>
            {
                ["MarketplaceId"] = _credentials.MarketplaceId
            };

            _logger.LogInformation("Validation endpoint: {Endpoint}", endpoint);
            _logger.LogInformation("Query parameters: {QueryParams}", System.Text.Json.JsonSerializer.Serialize(queryParams));

            var response = await _apiClient.GetAsync<List<GetShipmentResponse>>(endpoint, queryParams);

            var isValid = response.IsSuccess ||
                         (response.Errors.Count != 0 && !response.Errors.Any(e =>
                             e.Code.Contains("Unauthorized") ||
                             e.Code.Contains("InvalidAccessToken") ||
                             e.Code.Contains("AccessDenied")));

            _logger.LogInformation("=== ValidateCredentialsAsync RESULT ===");
            _logger.LogInformation("Credential validation result: {IsValid}", isValid);
            _logger.LogInformation("Response success: {Success}", response.IsSuccess);
            _logger.LogInformation("Error count: {ErrorCount}", response.Errors?.Count ?? 0);
            if (response.Errors?.Any() == true)
            {
                _logger.LogInformation("Validation errors: {Errors}", System.Text.Json.JsonSerializer.Serialize(response.Errors));
            }

            return isValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating credentials");
            return false;
        }
    }

    #region New v2024-03-20 API Helper Methods

    /// <summary>
    /// Generate packing options for an inbound plan
    /// </summary>
    private async Task<AmazonApiResponse<object>> GeneratePackingOptionsAsync(string inboundPlanId)
    {
        try
        {
            _logger.LogInformation("=== GeneratePackingOptionsAsync DEBUG ===");
            _logger.LogInformation("Method Entry - Inbound Plan ID: {InboundPlanId}", inboundPlanId);

            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingOptions";
            
            var result = await _apiClient.PostAsync<object>(endpoint, new { });

            _logger.LogInformation("=== GeneratePackingOptionsAsync RESULT ===");
            _logger.LogInformation("Success: {Success}", result.IsSuccess);
            _logger.LogInformation("Errors Count: {ErrorCount}", result.Errors?.Count ?? 0);
            if (result.Errors?.Any() == true)
            {
                _logger.LogInformation("Errors: {Errors}", System.Text.Json.JsonSerializer.Serialize(result.Errors));
            }
            if (result.Payload != null)
            {
                _logger.LogInformation("Response Payload: {Payload}", System.Text.Json.JsonSerializer.Serialize(result.Payload));
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating packing options for plan {InboundPlanId}", inboundPlanId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// List available packing options for an inbound plan
    /// </summary>
    private async Task<AmazonApiResponse<PackingOptionsResponse>> ListPackingOptionsAsync(string inboundPlanId)
    {
        try
        {
            _logger.LogInformation("=== ListPackingOptionsAsync DEBUG ===");
            _logger.LogInformation("Method Entry - Inbound Plan ID: {InboundPlanId}", inboundPlanId);

            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingOptions";
            
            var result = await _apiClient.GetAsync<PackingOptionsResponse>(endpoint);

            _logger.LogInformation("=== ListPackingOptionsAsync RESULT ===");
            _logger.LogInformation("Success: {Success}", result.IsSuccess);
            _logger.LogInformation("Errors Count: {ErrorCount}", result.Errors?.Count ?? 0);
            if (result.Errors?.Any() == true)
            {
                _logger.LogInformation("Errors: {Errors}", System.Text.Json.JsonSerializer.Serialize(result.Errors));
            }
            if (result.Payload != null)
            {
                _logger.LogInformation("Response Payload: {Payload}", System.Text.Json.JsonSerializer.Serialize(result.Payload));
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error listing packing options for plan {InboundPlanId}", inboundPlanId);
            return new AmazonApiResponse<PackingOptionsResponse>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Confirm a packing option for an inbound plan
    /// </summary>
    private async Task<AmazonApiResponse<object>> ConfirmPackingOptionAsync(string inboundPlanId, string packingOptionId)
    {
        try
        {
            _logger.LogInformation("=== ConfirmPackingOptionAsync DEBUG ===");
            _logger.LogInformation("Method Entry - Inbound Plan ID: {InboundPlanId}", inboundPlanId);
            _logger.LogInformation("Packing Option ID: {PackingOptionId}", packingOptionId);

            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingOptions/{packingOptionId}/confirmation";
            
            var result = await _apiClient.PostAsync<object>(endpoint, new { });

            _logger.LogInformation("=== ConfirmPackingOptionAsync RESULT ===");
            _logger.LogInformation("Success: {Success}", result.IsSuccess);
            _logger.LogInformation("Errors Count: {ErrorCount}", result.Errors?.Count ?? 0);
            if (result.Errors?.Any() == true)
            {
                _logger.LogInformation("Errors: {Errors}", System.Text.Json.JsonSerializer.Serialize(result.Errors));
            }
            if (result.Payload != null)
            {
                _logger.LogInformation("Response Payload: {Payload}", System.Text.Json.JsonSerializer.Serialize(result.Payload));
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error confirming packing option {PackingOptionId} for plan {InboundPlanId}", packingOptionId, inboundPlanId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Confirm placement option for an inbound plan
    /// </summary>
    private async Task<AmazonApiResponse<CreateInboundShipmentResponse>> ConfirmPlacementOptionAsync(string inboundPlanId, string packingOptionId)
    {
        try
        {
            _logger.LogInformation("=== ConfirmPlacementOptionAsync DEBUG ===");
            _logger.LogInformation("Method Entry - Inbound Plan ID: {InboundPlanId}", inboundPlanId);
            _logger.LogInformation("Packing Option ID (used as placement option): {PackingOptionId}", packingOptionId);

            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/placementOptions/{packingOptionId}/confirmation";
            
            var result = await _apiClient.PostAsync<CreateInboundShipmentResponse>(endpoint, new { });

            _logger.LogInformation("=== ConfirmPlacementOptionAsync RESULT ===");
            _logger.LogInformation("Success: {Success}", result.IsSuccess);
            _logger.LogInformation("Errors Count: {ErrorCount}", result.Errors?.Count ?? 0);
            if (result.Errors?.Any() == true)
            {
                _logger.LogInformation("Errors: {Errors}", System.Text.Json.JsonSerializer.Serialize(result.Errors));
            }
            if (result.Payload != null)
            {
                _logger.LogInformation("Response Payload: {Payload}", System.Text.Json.JsonSerializer.Serialize(result.Payload));
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error confirming placement option for plan {InboundPlanId}", inboundPlanId);
            return new AmazonApiResponse<CreateInboundShipmentResponse>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Enhances workflow-specific errors for better user understanding
    /// </summary>
    private static List<ApiError> EnhanceWorkflowErrors(List<ApiError> originalErrors, string workflowStep)
    {
        var enhancedErrors = new List<ApiError>();

        foreach (var error in originalErrors)
        {
            var enhancedError = new ApiError
            {
                Code = error.Code ?? "UNKNOWN_ERROR",
                Details = error.Details
            };

            // Enhance error messages based on workflow step and error code
            enhancedError.Message = (error.Code?.ToUpper(), workflowStep) switch
            {
                ("INVALID_ARGUMENT", "packing options generation") =>
                    "Invalid shipment data for packing options. Please check item quantities and dimensions.",
                ("RESOURCE_NOT_FOUND", _) =>
                    $"Inbound plan not found during {workflowStep}. The plan may have expired or been deleted.",
                ("PERMISSION_DENIED", _) =>
                    $"Access denied during {workflowStep}. Please check your API permissions.",
                ("QUOTA_EXCEEDED", _) =>
                    $"Rate limit exceeded during {workflowStep}. Please wait before retrying.",
                ("INTERNAL_SERVER_ERROR", _) =>
                    $"Amazon's servers encountered an error during {workflowStep}. Please try again later.",
                ("DEPRECATED_API", _) =>
                    "This API version is deprecated. The application needs to be updated to use the latest API version.",
                _ => string.IsNullOrEmpty(error.Message) ?
                    $"Error during {workflowStep}: {error.Code}" :
                    $"{error.Message} (during {workflowStep})"
            };

            enhancedErrors.Add(enhancedError);
        }

        return enhancedErrors;
    }

    #endregion

    /// <summary>
    /// Generate placement options for destination fulfillment centers
    /// Required for India marketplace workflow (Step 2)
    /// For India marketplace, this is where we specify the destination fulfillment center and items using customPlacement
    /// </summary>
    public async Task<AmazonApiResponse<object>> GeneratePlacementOptionsAsync(string inboundPlanId, string? destinationFulfillmentCenterId = null, List<ShipmentItem>? items = null)
    {
        try
        {
            _logger.LogInformation("Generating placement options for inbound plan {InboundPlanId} with destination FC {DestinationFC}",
                inboundPlanId, destinationFulfillmentCenterId);

            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/placementOptions";

            // For India marketplace, use customPlacement in the request body instead of query parameter
            object requestPayload;
            if (!string.IsNullOrEmpty(destinationFulfillmentCenterId) && items != null && items.Any())
            {
                _logger.LogInformation("Using customPlacement for India marketplace with destination FC: {DestinationFC}", destinationFulfillmentCenterId);

                // Create custom placement for India marketplace
                // Note: customPlacement should be an array with warehouseId (not fulfillmentCenterId)
                var customPlacement = new
                {
                    customPlacement = new[]
                    {
                        new
                        {
                            warehouseId = destinationFulfillmentCenterId,
                            items = items.Select(item => new
                            {
                                msku = item.Sku,
                                quantity = item.Quantity,
                                labelOwner = "SELLER",  // Required: AMAZON, NONE, or SELLER
                                prepOwner = "SELLER"    // Required: AMAZON, NONE, or SELLER
                            }).ToList()
                        }
                    }
                };

                requestPayload = customPlacement;
                _logger.LogInformation("Custom placement payload: {Payload}", System.Text.Json.JsonSerializer.Serialize(customPlacement));
            }
            else
            {
                // For other marketplaces or when no specific FC is provided, use empty body
                requestPayload = new { };
                _logger.LogInformation("Using empty payload for standard placement options generation");
            }

            return await _apiClient.PostAsync<object>(endpoint, requestPayload);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating placement options for plan {InboundPlanId}", inboundPlanId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// List placement options for destination fulfillment centers
    /// Required for India marketplace workflow (Step 2)
    /// </summary>
    public async Task<AmazonApiResponse<object>> ListPlacementOptionsAsync(string inboundPlanId)
    {
        try
        {
            _logger.LogInformation("Listing placement options for inbound plan {InboundPlanId}", inboundPlanId);

            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/placementOptions";

            return await _apiClient.GetAsync<object>(endpoint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error listing placement options for plan {InboundPlanId}", inboundPlanId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Set packing information for shipments
    /// Required for India marketplace workflow (Step 3)
    /// </summary>
    public async Task<AmazonApiResponse<object>> SetPackingInformationAsync(string inboundPlanId, object packingInformation)
    {
        try
        {
            _logger.LogInformation("Setting packing information for inbound plan {InboundPlanId}", inboundPlanId);

            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingInformation";

            return await _apiClient.PostAsync<object>(endpoint, packingInformation);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting packing information for plan {InboundPlanId}", inboundPlanId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Confirm placement options for an inbound plan
    /// Required for India marketplace workflow (Step 4)
    /// </summary>
    public async Task<AmazonApiResponse<object>> ConfirmPlacementOptionForIndiaPlanAsync(string inboundPlanId, string placementOptionId)
    {
        try
        {
            _logger.LogInformation("Confirming placement option {PlacementOptionId} for inbound plan {InboundPlanId}", placementOptionId, inboundPlanId);

            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/placementOptions/{placementOptionId}/confirmation";

            var requestPayload = new { placementOptionId };

            return await _apiClient.PostAsync<object>(endpoint, requestPayload);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error confirming placement option for plan {InboundPlanId}", inboundPlanId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Get shipment details using v2024-03-20 API
    /// </summary>
    public async Task<AmazonApiResponse<object>> GetShipmentDetailsAsync(string inboundPlanId, string shipmentId)
    {
        try
        {
            _logger.LogInformation("Getting shipment details for plan {InboundPlanId}, shipment {ShipmentId}", inboundPlanId, shipmentId);

            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}";

            return await _apiClient.GetAsync<object>(endpoint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting shipment details for plan {InboundPlanId}, shipment {ShipmentId}", inboundPlanId, shipmentId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Generate self-ship appointment slots (Step 7 - self-ship only)
    /// </summary>
    public async Task<AmazonApiResponse<object>> GenerateSelfShipAppointmentSlotsAsync(string inboundPlanId, string shipmentId, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            _logger.LogInformation("Generating self-ship appointment slots for plan {InboundPlanId}, shipment {ShipmentId}", inboundPlanId, shipmentId);

            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/selfShipAppointmentSlots";

            var requestPayload = new
            {
                startDate = startDate?.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                endDate = endDate?.ToString("yyyy-MM-ddTHH:mm:ssZ")
            };

            return await _apiClient.PostAsync<object>(endpoint, requestPayload);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating self-ship appointment slots for plan {InboundPlanId}, shipment {ShipmentId}", inboundPlanId, shipmentId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Get available self-ship appointment slots (Step 8 - self-ship only)
    /// </summary>
    public async Task<AmazonApiResponse<object>> GetSelfShipAppointmentSlotsAsync(string inboundPlanId, string shipmentId)
    {
        try
        {
            _logger.LogInformation("Getting self-ship appointment slots for plan {InboundPlanId}, shipment {ShipmentId}", inboundPlanId, shipmentId);

            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/selfShipAppointmentSlots";

            return await _apiClient.GetAsync<object>(endpoint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting self-ship appointment slots for plan {InboundPlanId}, shipment {ShipmentId}", inboundPlanId, shipmentId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Schedule a self-ship appointment slot (Step 8 - self-ship only)
    /// </summary>
    public async Task<AmazonApiResponse<object>> ScheduleSelfShipAppointmentAsync(string inboundPlanId, string shipmentId, string slotId, string? reasonComment = null)
    {
        try
        {
            _logger.LogInformation("Scheduling self-ship appointment slot {SlotId} for plan {InboundPlanId}, shipment {ShipmentId}", slotId, inboundPlanId, shipmentId);

            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/selfShipAppointmentSlots/{slotId}/schedule";

            var requestPayload = new
            {
                slotId,
                reasonComment
            };

            return await _apiClient.PostAsync<object>(endpoint, requestPayload);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling self-ship appointment for plan {InboundPlanId}, shipment {ShipmentId}", inboundPlanId, shipmentId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Cancel a self-ship appointment slot
    /// </summary>
    public async Task<AmazonApiResponse<object>> CancelSelfShipAppointmentAsync(string inboundPlanId, string shipmentId)
    {
        try
        {
            _logger.LogInformation("Canceling self-ship appointment for plan {InboundPlanId}, shipment {ShipmentId}", inboundPlanId, shipmentId);

            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/selfShipAppointmentSlots/cancellation";

            return await _apiClient.PostAsync<object>(endpoint, new { });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error canceling self-ship appointment for plan {InboundPlanId}, shipment {ShipmentId}", inboundPlanId, shipmentId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Get inbound operation status (used to check async operation status)
    /// </summary>
    public async Task<AmazonApiResponse<object>> GetInboundOperationStatusAsync(string operationId)
    {
        try
        {
            _logger.LogInformation("Getting inbound operation status for operation {OperationId}", operationId);

            var endpoint = $"/inbound/fba/2024-03-20/operations/{operationId}";

            return await _apiClient.GetAsync<object>(endpoint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting operation status for operation {OperationId}", operationId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Get delivery challan document (India marketplace specific)
    /// </summary>
    public async Task<AmazonApiResponse<object>> GetDeliveryChallanDocumentAsync(string inboundPlanId, string shipmentId)
    {
        try
        {
            _logger.LogInformation("Getting delivery challan document for plan {InboundPlanId}, shipment {ShipmentId}", inboundPlanId, shipmentId);

            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/deliveryChallanDocument";

            return await _apiClient.GetAsync<object>(endpoint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delivery challan document for plan {InboundPlanId}, shipment {ShipmentId}", inboundPlanId, shipmentId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }
}
