namespace SilvrBear_Amazon_Automation.Constants;

/// <summary>
/// Central repository for all application constants used in Amazon FBA shipment processing.
/// 
/// This class contains all hardcoded values that were previously scattered throughout the application,
/// now organized into logical groups for better maintainability and consistency.
/// 
/// Organization:
/// - ShipFromAddress: Physical address details for shipment origin
/// - FulfillmentCenters: Amazon FC details and mappings
/// - Defaults: Default values for shipment creation and API calls
/// - ShipmentStatus: Status definitions and UI color mappings
/// - OpenAI: AI service configuration and prompts
/// - FileProcessing: File handling and temp directory settings
/// - WeightAndDimensions: Unit conversions and default weights
/// - Validation: Input validation limits and constraints
/// - ErrorMessages: Standardized error message templates
/// - SuccessMessages: Standardized success message templates
/// 
/// Note: Environment-specific values (API keys, addresses) are moved to appsettings.json
/// and accessed via IOptions&lt;T&gt; pattern in services.
/// </summary>
public static class ShipmentConstants
{
    /// <summary>
    /// Default ship from address for all shipments (moved to appsettings.json)
    /// </summary>
    [Obsolete("Ship from address is now configured in appsettings.json")]
    public static class ShipFromAddress
    {
        // This class is kept for backward compatibility but values should come from appsettings.json
    }

    /// <summary>
    /// Amazon fulfillment center details for India
    /// </summary>
    public static class FulfillmentCenters
    {
        /// <summary>
        /// Default fulfillment center - ISK3 (India)
        /// </summary>
        public const string DefaultFulfillmentCenterId = "ISK3";
        public const string DefaultFulfillmentCenterName = "Amazon Fulfillment Center ISK3";
        
        /// <summary>
        /// Available fulfillment centers in India
        /// </summary>
        public static readonly Dictionary<string, string> IndiaFulfillmentCenters = new()
        {
            { "ISK3", "Amazon FC ISK3 - Mumbai" },
            { "ISK1", "Amazon FC ISK1 - Delhi" },
            { "ISK2", "Amazon FC ISK2 - Bangalore" },
            { "ISK4", "Amazon FC ISK4 - Chennai" },
            { "ISK5", "Amazon FC ISK5 - Hyderabad" }
        };

        /// <summary>
        /// Complete fulfillment center data for India
        /// </summary>
        public static readonly Dictionary<string, (string Name, string Address, string City, string StateOrProvince, string CountryCode, string PostalCode)> FulfillmentCenterDetails = new()
        {
            {
                "ISK3",
                (
                    Name: "Amazon Fulfillment Center ISK3 - Mumbai",
                    Address: "Amazon Fulfillment Center",
                    City: "Mumbai",
                    StateOrProvince: "Maharashtra",
                    CountryCode: "IN",
                    PostalCode: "400001"
                )
            },
            {
                "ISK1",
                (
                    Name: "Amazon Fulfillment Center ISK1 - Delhi",
                    Address: "Amazon Fulfillment Center",
                    City: "Delhi",
                    StateOrProvince: "Delhi",
                    CountryCode: "IN",
                    PostalCode: "110001"
                )
            },
            {
                "ISK2",
                (
                    Name: "Amazon Fulfillment Center ISK2 - Bangalore",
                    Address: "Amazon Fulfillment Center",
                    City: "Bangalore",
                    StateOrProvince: "Karnataka",
                    CountryCode: "IN",
                    PostalCode: "560001"
                )
            },
            {
                "ISK4",
                (
                    Name: "Amazon Fulfillment Center ISK4 - Chennai",
                    Address: "Amazon Fulfillment Center",
                    City: "Chennai",
                    StateOrProvince: "Tamil Nadu",
                    CountryCode: "IN",
                    PostalCode: "600001"
                )
            },
            {
                "ISK5",
                (
                    Name: "Amazon Fulfillment Center ISK5 - Hyderabad",
                    Address: "Amazon Fulfillment Center",
                    City: "Hyderabad",
                    StateOrProvince: "Telangana",
                    CountryCode: "IN",
                    PostalCode: "500001"
                )
            }
        };
    }

    /// <summary>
    /// Shipment status definitions and UI mappings
    /// </summary>
    public static class ShipmentStatus
    {
        public static readonly string[] AllStatuses = {
            "WORKING", "SHIPPED", "RECEIVING", "CANCELLED", 
            "DELETED", "CLOSED", "ERROR", "IN_TRANSIT", 
            "DELIVERED", "CHECKED_IN"
        };
        
        public static readonly Dictionary<string, string> StatusColors = new()
        {
            { "WORKING", "primary" },
            { "SHIPPED", "info" },
            { "RECEIVING", "warning" },
            { "DELIVERED", "success" },
            { "CANCELLED", "danger" },
            { "DELETED", "secondary" },
            { "CLOSED", "secondary" },
            { "ERROR", "danger" },
            { "IN_TRANSIT", "info" },
            { "CHECKED_IN", "warning" }
        };
    }

    /// <summary>
    /// OpenAI API configuration (moved to appsettings.json)
    /// </summary>
    public static class OpenAI
    {
        /// <summary>
        /// System prompt for image processing
        /// </summary>
        public const string ImageProcessingPrompt = @"
You are an expert data extraction assistant. Analyze the handwritten table in the image and extract shipment box details.

The image contains a table with columns for:
- Box No. (Box number)
- Product Name (Product name)
- Barcode Type (old/new)
- Quantity (Number of items)
- Dimensions (Format: LxWxH like 58X38X26)
- Remarks (Additional remarks if any)

Extract ALL visible rows from the table and return the data in the following JSON format:
{
  ""boxes"": [
    {
      ""boxNumber"": 1,
      ""productName"": ""Skinny Black"",
      ""barcodeType"": ""old"",
      ""quantity"": 200,
      ""dimensions"": ""58X38X26"",
      ""remarks"": ""269 To 300""
    }
  ]
}

Important rules:
1.When extracting product names from the image, match each product name with the closest match from the provided catalog ONLY. Use similarity algorithms such as Levenshtein distance or cosine similarity to ensure the best match.
- If an exact match is not found, choose the most semantically similar product name from the catalog. Do not introduce new product names or leave fields empty.
- Prioritize accuracy in mapping product names while keeping all other extracted text unchanged.
2. Include all visible boxes, even if some fields are empty
3. For dimensions, maintain the exact format (e.g., 58X38X26)
4. For remarks, text as provided in image
5. Return valid JSON only, no additional text or explanations
";
    }

    /// <summary>
    /// Amazon Seller API configuration for India
    /// </summary>
    public static class AmazonAPI
    {
        public const string IndiaMarketplaceId = "A21TJRUUN4KGV"; // Amazon India marketplace
        public const string IndiaRegion = "eu-west-1"; // Amazon India uses EU West region
        public const string IndiaBaseUrl = "https://sellingpartnerapi-eu.amazon.com";
        public const string IndiaCountryCode = "IN"; // India country code

        /// <summary>
        /// Default shipment preferences for India
        /// </summary>
        public const string DefaultLabelPrepPreference = "SELLER_LABEL";
        public const string DefaultShipmentType = "SP"; // Small Parcel
        public const bool DefaultAreCasesRequired = false;
        public const string DefaultShipToCountryCode = "IN";

        /// <summary>
        /// Booking preferences
        /// </summary>
        public const string PreferredTimeSlot = "EARLIEST_AVAILABLE";
        public const int BookingAdvanceDays = 1; // Book 1 day in advance

        /// <summary>
        /// India-specific API settings
        /// </summary>
        public const string IndiaLwaTokenUrl = "https://api.amazon.com/auth/o2/token";
        public const string IndiaAwsRegion = "eu-west-1";
    }

    /// <summary>
    /// File processing constants
    /// </summary>
    public static class FileProcessing
    {
        public const int MaxImageSizeMB = 10;
        public const int MaxImageSizeBytes = MaxImageSizeMB * 1024 * 1024;
        
        public static readonly string[] SupportedImageFormats = { ".jpg", ".jpeg", ".png", ".bmp", ".gif" };
        public static readonly string[] SupportedExcelFormats = { ".xlsx", ".xls" };
        
        public const string MappingFileName = "Mapping.xlsx";
        public const string SampleImageFileName = "sample-image.jpg";
        public const string TempImageDirectory = "temp/images";
    }

    /// <summary>
    /// Weight and dimension conversion constants
    /// </summary>
    public static class Conversions
    {
        public const decimal CmToInches = 0.393701m;
        public const decimal GramsToPounds = 0.00220462m;
        public const decimal KgToPounds = 2.20462m;
        
        /// <summary>
        /// Default weights when not specified (in grams)
        /// </summary>
        public const decimal DefaultItemWeightGrams = 250m;
        public const decimal DefaultBoxWeightGrams = 100m; // Empty box weight
    }

    /// <summary>
    /// Validation constants
    /// </summary>
    public static class Validation
    {
        public const int MinBoxNumber = 1;
        public const int MaxBoxNumber = 999;
        public const int MinQuantity = 1;
        public const int MaxQuantity = 10000;
        public const decimal MinDimension = 0.1m;
        public const decimal MaxDimension = 200m; // 200 cm max
        public const decimal MinWeight = 0.01m;
        public const decimal MaxWeight = 50m; // 50 kg max per box
        
        public const int MaxProductNameLength = 100;
        public const int MaxRemarksLength = 500;
    }

    /// <summary>
    /// Error messages
    /// </summary>
    public static class ErrorMessages
    {
        public const string InvalidImageFormat = "Invalid image format. Supported formats: JPG, PNG, BMP, GIF";
        public const string ImageTooLarge = "Image size exceeds maximum limit of {0}MB";
        public const string NoBoxesFound = "No valid boxes found in the image";
        public const string ProductNotFound = "Product '{0}' not found in mapping file";
        public const string InvalidDimensions = "Invalid dimensions format. Expected format: LxWxH (e.g., 58X38X26)";
        public const string InvalidWeight = "Invalid weight format. Expected format: 'min To max' (e.g., 269 To 300)";
        public const string OpenAIProcessingFailed = "Failed to process image with OpenAI: {0}";
        public const string AmazonAPIFailed = "Amazon API call failed: {0}";
        public const string MappingFileNotFound = "Product mapping file not found: {0}";
    }

    /// <summary>
    /// Success messages
    /// </summary>
    public static class SuccessMessages
    {
        public const string ImageProcessedSuccessfully = "Image processed successfully. Found {0} boxes.";
        public const string ShipmentCreatedSuccessfully = "Inbound shipment created successfully. Shipment ID: {0}";
        public const string DataValidatedSuccessfully = "All data validated successfully. Ready to create shipment.";
        public const string MappingLoadedSuccessfully = "Product mapping loaded successfully. {0} products available.";
    }
}
