using SilvrBear_Amazon_Automation.Services;
using SilvrBear_Amazon_Automation.Models;
using Microsoft.Extensions.Options;

namespace SilvrBear_Amazon_Automation;

/// <summary>
/// Test class to demonstrate the enhanced API logging functionality
/// This class provides examples of how to test the logging output
/// </summary>
public class TestApiLogging
{
    private readonly IAmazonSellerApiService _amazonService;
    private readonly ILogger<TestApiLogging> _logger;

    public TestApiLogging(IAmazonSellerApiService amazonService, ILogger<TestApiLogging> logger)
    {
        _amazonService = amazonService;
        _logger = logger;
    }

    /// <summary>
    /// Test method to demonstrate API logging with credential validation
    /// This will show all request/response logging in the debug console
    /// </summary>
    public async Task TestCredentialValidationLogging()
    {
        _logger.LogInformation("=== TESTING API LOGGING ===");
        _logger.LogInformation("Starting credential validation test to demonstrate API logging...");

        try
        {
            var isValid = await _amazonService.ValidateCredentialsAsync();
            
            _logger.LogInformation("Credential validation completed. Result: {IsValid}", isValid);
            _logger.LogInformation("Check the console output above for detailed API request/response logging.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during credential validation test");
        }

        _logger.LogInformation("=== API LOGGING TEST COMPLETE ===");
    }

    /// <summary>
    /// Test method to demonstrate API logging with a sample inbound plan creation
    /// This will show comprehensive logging for the entire workflow
    /// </summary>
    public async Task TestInboundPlanCreationLogging()
    {
        _logger.LogInformation("=== TESTING INBOUND PLAN CREATION LOGGING ===");
        _logger.LogInformation("Starting inbound plan creation test to demonstrate comprehensive API logging...");

        try
        {
            // Sample data for testing
            var sampleItems = new List<ShipmentItem>
            {
                new() { Sku = "TEST-SKU-001", Quantity = 10 },
                new() { Sku = "TEST-SKU-002", Quantity = 5 }
            };

            var sampleAddress = new Address
            {
                Name = "Test Warehouse",
                AddressLine1 = "123 Test Street",
                City = "Test City",
                StateOrProvinceCode = "TS",
                CountryCode = "US",
                PostalCode = "12345",
                PhoneNumber = "******-123-4567"
            };

            var result = await _amazonService.CreateInboundShipmentPlanAsync(
                sampleItems, 
                sampleAddress, 
                "SELLER_LABEL"
            );

            _logger.LogInformation("Inbound plan creation completed. Success: {Success}", result.IsSuccess);
            _logger.LogInformation("Check the console output above for detailed API request/response logging throughout the workflow.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during inbound plan creation test");
        }

        _logger.LogInformation("=== INBOUND PLAN CREATION LOGGING TEST COMPLETE ===");
    }

    /// <summary>
    /// Instructions for using the enhanced logging
    /// </summary>
    public void LogUsageInstructions()
    {
        _logger.LogInformation("=== API LOGGING USAGE INSTRUCTIONS ===");
        _logger.LogInformation("The enhanced API logging will now show:");
        _logger.LogInformation("1. Method entry with parameter details");
        _logger.LogInformation("2. Request URLs, headers, and payloads");
        _logger.LogInformation("3. Response status codes, headers, and content");
        _logger.LogInformation("4. Step-by-step workflow progress");
        _logger.LogInformation("5. Detailed error information");
        _logger.LogInformation("");
        _logger.LogInformation("All API calls to Amazon Seller API will now include:");
        _logger.LogInformation("- Complete request/response data");
        _logger.LogInformation("- Method parameter serialization");
        _logger.LogInformation("- Workflow step tracking");
        _logger.LogInformation("- Enhanced error messages");
        _logger.LogInformation("");
        _logger.LogInformation("Look for log entries with patterns like:");
        _logger.LogInformation("- '=== [METHOD NAME] DEBUG ==='");
        _logger.LogInformation("- '=== [REQUEST TYPE] REQUEST DEBUG ==='");
        _logger.LogInformation("- '=== API RESPONSE DEBUG ==='");
        _logger.LogInformation("- '=== [METHOD NAME] RESULT ==='");
        _logger.LogInformation("=== END USAGE INSTRUCTIONS ===");
    }
}
